server:
  port: 8090
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  autoconfigure:
    #    先强制关闭 openai 的自动配置
    exclude: org.springframework.ai.autoconfigure.openai.OpenAiAutoConfiguration
  http:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  datasource:
    driver-class-name: org.postgresql.Driver
    username: postgres
    password: pgvector123
    url: jdbc:postgresql://*************:15432/ai-rag-knowledge
    type: com.zaxxer.hikari.HikariDataSource
    # hikari连接池配置
    hikari:
      #连接池名
      pool-name: HikariCP
      #最小空闲连接数
      minimum-idle: 5
      # 空闲连接存活最大时间，默认10分钟
      idle-timeout: 600000
      # 连接池最大连接数，默认是10
      maximum-pool-size: 10
      # 此属性控制从池返回的连接的默认自动提交行为,默认值：true
      auto-commit: true
      # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
      max-lifetime: 1800000
      # 数据库连接超时时间,默认30秒
      connection-timeout: 30000
      # 连接测试query
      connection-test-query: SELECT 1
  ai:
    ollama:
      base-url: http://localhost:11434
      embedding:
        model: nomic-embed-text # 嵌入模型
        options:
          num-batch: 512 # 批次大小
    openai:
      base-url: https://api.siliconflow.cn
      api-key: sk-bpbqulqtsmbywglsemzqantxqhmilksogyeitgcpkbvwioix
      chat:
        options:
          model: Qwen/Qwen2.5-7B-Instruct
      embedding:
        options:
          model: BAAI/bge-large-zh-v1.5
    rag:
      ## 设置嵌入模型
      #      embed: nomic-embed-text #nomic-embed-text、BAAI/bge-m3
      embed: BAAI/bge-large-zh-v1.5
## Redis
redis:
  sdk:
    config:
      host: *************
      port: 6379
      pool-size: 10
      min-idle-size: 5
      idle-timeout: 30000
      connect-timeout: 5000
      retry-attempts: 3
      retry-interval: 1000
      ping-interval: 60000
      keep-alive: true
      password: redis_bXfhKC

## 文件服务器 minio
minio:
  url: http://*************:9000
  access-key: Zg6N7Veb1oBZEpzqupJ4
  secret-key: CyBD10UpQ37jQ8WcsJzXwocGw7tH4a6LQiWTgInB
  bucket-name: knowledge

## 日志配置
logging:
  level:
    root: info
  config: classpath:logback-spring.xml
