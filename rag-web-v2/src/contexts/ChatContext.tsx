import React, { createContext, useContext, useState, useEffect, useCallback } from 'react'
import { storageService } from '@/services'
import type { Chat, Message } from '@/types'

interface ChatContextType {
  // 当前聊天
  currentChat: Chat | null
  setCurrentChat: (chat: Chat | null) => void
  
  // 聊天列表
  chats: Chat[]
  refreshChats: () => void
  
  // 聊天操作
  createNewChat: (title?: string, ragTag?: string) => Chat
  deleteChat: (chatId: string) => void
  updateChatTitle: (chatId: string, title: string) => void
  
  // 消息操作
  addMessage: (message: Omit<Message, 'id' | 'timestamp'>) => Message
  updateMessage: (messageId: string, updates: Partial<Message>) => void
  clearCurrentChat: () => void
  
  // 导出功能
  exportChat: (chatId: string) => string
  exportAllChats: () => string
}

const ChatContext = createContext<ChatContextType | undefined>(undefined)

export function useChatContext() {
  const context = useContext(ChatContext)
  if (context === undefined) {
    throw new Error('useChatContext must be used within a ChatProvider')
  }
  return context
}

interface ChatProviderProps {
  children: React.ReactNode
}

export function ChatProvider({ children }: ChatProviderProps) {
  const [currentChat, setCurrentChat] = useState<Chat | null>(null)
  const [chats, setChats] = useState<Chat[]>([])

  // 刷新聊天列表
  const refreshChats = useCallback(() => {
    const savedChats = storageService.getChats()
    // 按更新时间倒序排列
    const sortedChats = savedChats.sort((a, b) => b.updatedAt - a.updatedAt)
    setChats(sortedChats)
  }, [])

  // 初始化时加载聊天列表
  useEffect(() => {
    refreshChats()
  }, [refreshChats])

  // 创建新聊天
  const createNewChat = useCallback((title?: string, ragTag?: string): Chat => {
    const newChat: Chat = {
      id: `chat_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      title: title || '新对话',
      messages: [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
      ragTag,
    }

    storageService.saveChat(newChat)
    refreshChats()
    setCurrentChat(newChat)
    
    return newChat
  }, [refreshChats])

  // 删除聊天
  const deleteChat = useCallback((chatId: string) => {
    storageService.deleteChat(chatId)
    
    // 如果删除的是当前聊天，清空当前聊天
    if (currentChat?.id === chatId) {
      setCurrentChat(null)
    }
    
    refreshChats()
  }, [currentChat, refreshChats])

  // 更新聊天标题
  const updateChatTitle = useCallback((chatId: string, title: string) => {
    const chat = storageService.getChat(chatId)
    if (chat) {
      chat.title = title
      chat.updatedAt = Date.now()
      storageService.saveChat(chat)
      
      // 如果是当前聊天，更新当前聊天状态
      if (currentChat?.id === chatId) {
        setCurrentChat({ ...chat })
      }
      
      refreshChats()
    }
  }, [currentChat, refreshChats])

  // 添加消息
  const addMessage = useCallback((messageData: Omit<Message, 'id' | 'timestamp'>): Message => {
    if (!currentChat) {
      throw new Error('No current chat selected')
    }

    const message: Message = {
      ...messageData,
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      timestamp: Date.now(),
    }

    storageService.addMessageToChat(currentChat.id, message)
    
    // 更新当前聊天状态
    const updatedChat = storageService.getChat(currentChat.id)
    if (updatedChat) {
      setCurrentChat(updatedChat)
      
      // 如果是第一条用户消息，自动生成标题
      if (updatedChat.messages.length === 1 && message.role === 'user' && updatedChat.title === '新对话') {
        const title = message.content.length > 20 
          ? message.content.substring(0, 20) + '...' 
          : message.content
        updateChatTitle(currentChat.id, title)
      }
    }
    
    refreshChats()
    return message
  }, [currentChat, refreshChats, updateChatTitle])

  // 更新消息
  const updateMessage = useCallback((messageId: string, updates: Partial<Message>) => {
    if (!currentChat) {
      return
    }

    storageService.updateMessageInChat(currentChat.id, messageId, updates)
    
    // 更新当前聊天状态
    const updatedChat = storageService.getChat(currentChat.id)
    if (updatedChat) {
      setCurrentChat(updatedChat)
    }
    
    refreshChats()
  }, [currentChat, refreshChats])

  // 清空当前聊天
  const clearCurrentChat = useCallback(() => {
    if (!currentChat) {
      return
    }

    const clearedChat: Chat = {
      ...currentChat,
      messages: [],
      updatedAt: Date.now(),
    }

    storageService.saveChat(clearedChat)
    setCurrentChat(clearedChat)
    refreshChats()
  }, [currentChat, refreshChats])

  // 导出单个聊天
  const exportChat = useCallback((chatId: string): string => {
    const chat = storageService.getChat(chatId)
    if (!chat) {
      throw new Error('Chat not found')
    }

    const exportData = {
      title: chat.title,
      createdAt: new Date(chat.createdAt).toLocaleString(),
      updatedAt: new Date(chat.updatedAt).toLocaleString(),
      ragTag: chat.ragTag,
      messages: chat.messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        timestamp: new Date(msg.timestamp).toLocaleString(),
      })),
    }

    return JSON.stringify(exportData, null, 2)
  }, [])

  // 导出所有聊天
  const exportAllChats = useCallback((): string => {
    const exportData = storageService.exportData()
    return JSON.stringify(exportData, null, 2)
  }, [])

  const value: ChatContextType = {
    currentChat,
    setCurrentChat,
    chats,
    refreshChats,
    createNewChat,
    deleteChat,
    updateChatTitle,
    addMessage,
    updateMessage,
    clearCurrentChat,
    exportChat,
    exportAllChats,
  }

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  )
}
