// API 相关类型
export type ApiType = 'OLLAMA' | 'OPENAI'

export interface ApiConfig {
    BASE_URL: string
    ENDPOINTS: {
        OLLAMA: {
            CHAT: string
            CHAT_RAG: string
            GENERATE: string
        }
        OPENAI: {
            CHAT: string
            CHAT_RAG: string
            GENERATE: string
        }
        RAG: {
            UPLOAD: string
            TAGS: string
            GIT_UPLOAD: string
        }
        MODEL: {
            LIST: string
        }
    }
    MODELS: {
        OLLAMA: string[]
        OPENAI: string[]
    }
}

export interface ApiResponse<T = any> {
    code: string
    info: string
    data: T
}

// 消息相关类型
export interface Message {
    id: string
    role: 'user' | 'assistant'
    content: string
    timestamp: number
    ragTag?: string
    isStreaming?: boolean
}

export interface ChatResponse {
    message: string
    model?: string
    timestamp?: number
}

// 聊天相关类型
export interface Chat {
    id: string
    title: string
    messages: Message[]
    createdAt: number
    updatedAt: number
    ragTag?: string
}

// 应用配置类型
export interface AppConfig {
    apiType: ApiType
    selectedModel: string
    selectedRagTag: string
    theme: 'light' | 'dark'
}

// RAG 相关类型
export interface RagTag {
    id: string
    name: string
    description?: string
    createdAt: number
}

export interface GitRepoUpload {
    ragTag: string
    repoUrl: string
    branch?: string
    accessToken?: string
    username?: string
    password?: string
}

// 主题相关类型
export type Theme = 'light' | 'dark' | 'system'