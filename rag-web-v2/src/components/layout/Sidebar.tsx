import { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import {
  MessageCircle,
  Plus,
  Upload,
  Settings,
  Moon,
  Sun,
  X,
} from 'lucide-react'
import { useTheme } from '@/components/theme-provider'
import { useConfig } from '@/contexts/ConfigContext'
import { useChatContext } from '@/contexts/ChatContext'
import { modelApiService, ragApiService } from '@/services'
import UploadManager from '@/components/upload/UploadManager'
import ChatHistory from '@/components/chat/ChatHistory'
import type { ApiType } from '@/types'

interface SidebarProps {
  isOpen: boolean
  onClose: () => void
  isMobile: boolean
}

export default function Sidebar({ isOpen, onClose, isMobile }: SidebarProps) {
  const { theme, setTheme } = useTheme()
  const { config, updateConfig } = useConfig()
  const { createNewChat } = useChatContext()
  const [models, setModels] = useState<string[]>([])
  const [tags, setTags] = useState<string[]>([])
  const [showUploadManager, setShowUploadManager] = useState(false)

  // 拉取模型与标签
  useEffect(() => {
    modelApiService.getModels(config.apiType).then(setModels)
    ragApiService.getRagTags().then(setTags)
  }, [config.apiType])

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark')
  }

  const handleUploadComplete = (message: string) => {
    // 刷新标签列表
    ragApiService.getRagTags().then(setTags)
    // 关闭上传管理器
    setShowUploadManager(false)
    // 可以在这里显示成功消息
    console.log('Upload completed:', message)
  }

  const handleNewChat = () => {
    createNewChat('新对话', config.selectedRagTag)
  }

  if (!isOpen) return null

  return (
    <div className="flex flex-col h-full w-80 bg-card border-r">
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
            <MessageCircle className="w-5 h-5 text-primary" />
          </div>
          <h1 className="text-lg font-semibold">AI RAG Chat</h1>
        </div>
        {isMobile && (
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="w-5 h-5" />
          </Button>
        )}
      </div>

      {/* 配置区域 */}
      <div className="p-4 border-b">
        <Card className="p-4 space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              API类型
            </label>
            <select
              value={config.apiType}
              onChange={(e) => updateConfig({ apiType: e.target.value as ApiType })}
              className="w-full p-2 text-sm border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
            >
              <option value="OLLAMA">Ollama</option>
              <option value="OPENAI">OpenAI</option>
            </select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              选择模型
            </label>
            <select
              value={config.selectedModel}
              onChange={(e) => updateConfig({ selectedModel: e.target.value })}
              className="w-full p-2 text-sm border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
            >
              {(models.length > 0 ? models : (config.apiType === 'OLLAMA' ? ['qwen3', 'gemma3'] : ['Qwen/Qwen2.5-7B-Instruct', 'moonshotai/Kimi-K2-Instruct'])).map(m => (
                <option key={m} value={m}>{m}</option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              知识库标签
            </label>
            <select
              value={config.selectedRagTag}
              onChange={(e) => updateConfig({ selectedRagTag: e.target.value })}
              className="w-full p-2 text-sm border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
            >
              <option value="">选择知识库标签</option>
              {tags.map(tag => (
                <option key={tag} value={tag}>{tag}</option>
              ))}
            </select>
          </div>
        </Card>
      </div>

      {/* 新建对话按钮 */}
      <div className="p-4 border-b">
        <Button className="w-full" size="lg" onClick={handleNewChat}>
          <Plus className="w-4 h-4 mr-2" />
          新建对话
        </Button>
      </div>

      {/* 对话历史 */}
      <div className="flex-1 overflow-y-auto custom-scrollbar">
        <div className="p-4">
          <ChatHistory />
        </div>
      </div>

      {/* 底部操作区域 */}
      <div className="p-4 border-t space-y-2">
        <Button
          variant="ghost"
          className="w-full justify-start"
          size="sm"
          onClick={() => setShowUploadManager(true)}
        >
          <Upload className="w-4 h-4 mr-2" />
          上传知识库
        </Button>
        <Button variant="ghost" className="w-full justify-start" size="sm">
          <Settings className="w-4 h-4 mr-2" />
          设置
        </Button>
        <Button
          variant="ghost"
          className="w-full justify-start"
          size="sm"
          onClick={toggleTheme}
        >
          {theme === 'dark' ? (
            <Sun className="w-4 h-4 mr-2" />
          ) : (
            <Moon className="w-4 h-4 mr-2" />
          )}
          {theme === 'dark' ? '浅色模式' : '深色模式'}
        </Button>
      </div>

      {/* 上传管理器弹窗 */}
      {showUploadManager && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="max-w-4xl w-full mx-4">
            <UploadManager
              onUploadComplete={handleUploadComplete}
              onClose={() => setShowUploadManager(false)}
            />
          </div>
        </div>
      )}
    </div>
  )
}
