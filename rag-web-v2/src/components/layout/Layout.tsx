import { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { useResponsive } from '@/hooks/useResponsive'
import { useChatContext } from '@/contexts/ChatContext'
import Sidebar from './Sidebar'
import Header from './Header'
import MainContent from './MainContent'

interface LayoutProps {
  children?: React.ReactNode
}

export default function Layout({ children }: LayoutProps) {
  const { isMobile, isTablet } = useResponsive()
  const { currentChat, clearCurrentChat, exportChat } = useChatContext()
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile)

  // 当屏幕尺寸变化时调整侧边栏状态
  useEffect(() => {
    if (isMobile && sidebarOpen) {
      setSidebarOpen(false)
    } else if (!isMobile && !isTablet && !sidebarOpen) {
      setSidebarOpen(true)
    }
  }, [isMobile, isTablet, sidebarOpen])

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen)
  }

  const handleClearChat = () => {
    if (currentChat && confirm('确定要清空当前对话吗？此操作无法撤销。')) {
      clearCurrentChat()
    }
  }

  const handleExportChat = () => {
    if (currentChat) {
      try {
        const exportData = exportChat(currentChat.id)
        const filename = `chat_${currentChat.title}_${new Date().toISOString().split('T')[0]}.json`

        const blob = new Blob([exportData], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = filename
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      } catch (error) {
        console.error('Export failed:', error)
        alert('导出失败，请重试')
      }
    }
  }

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      {/* 移动端遮罩层 */}
      {isMobile && sidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* 侧边栏 */}
      <div
        className={cn(
          "relative z-50 transition-all duration-300 ease-in-out",
          "border-r bg-card/50 backdrop-blur-sm",
          isMobile
            ? sidebarOpen
              ? "fixed inset-y-0 left-0 w-80"
              : "w-0 overflow-hidden"
            : sidebarOpen
              ? "w-80"
              : "w-0 overflow-hidden"
        )}
      >
        <Sidebar
          isOpen={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          isMobile={isMobile}
        />
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col min-w-0">
        <Header
          sidebarOpen={sidebarOpen}
          onToggleSidebar={toggleSidebar}
          isMobile={isMobile}
          onClearChat={handleClearChat}
          onExportChat={handleExportChat}
        />
        <MainContent>
          {children}
        </MainContent>
      </div>
    </div>
  )
}
