import { useState, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  GitBranch,
  X,
  Loader2,
  CheckCircle,
  AlertCircle,
  Eye,
  EyeOff,
} from 'lucide-react'
import { ragApiService } from '@/services'
import { useConfig } from '@/contexts/ConfigContext'
import type { GitRepoUpload as GitRepoUploadType } from '@/types'

interface GitRepoUploadProps {
  onUploadComplete?: (message: string) => void
  onClose?: () => void
}

export default function GitRepoUpload({ onUploadComplete, onClose }: GitRepoUploadProps) {
  const { config } = useConfig()
  const [formData, setFormData] = useState<GitRepoUploadType>({
    ragTag: config.selectedRagTag || '',
    repoUrl: '',
    branch: 'main',
    username: '',
    password: '',
  })
  const [isUploading, setIsUploading] = useState(false)
  const [uploadStatus, setUploadStatus] = useState<string>('')
  const [showPassword, setShowPassword] = useState(false)
  const [uploadResult, setUploadResult] = useState<{
    type: 'success' | 'error'
    message: string
  } | null>(null)

  // 更新表单数据
  const updateFormData = useCallback((field: keyof GitRepoUploadType, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }, [])

  // 验证 Git 仓库 URL
  const validateRepoUrl = (url: string): boolean => {
    const gitUrlPattern = /^(https?:\/\/)?([\w\.-]+@)?([\w\.-]+)(:\d+)?(\/[\w\.-]+)*\.git$/i
    const githubPattern = /^https?:\/\/github\.com\/[\w\.-]+\/[\w\.-]+\/?$/i
    const gitlabPattern = /^https?:\/\/gitlab\.com\/[\w\.-]+\/[\w\.-]+\/?$/i

    return gitUrlPattern.test(url) || githubPattern.test(url) || gitlabPattern.test(url)
  }

  // 处理上传
  const handleUpload = useCallback(async () => {
    // 验证必填字段
    if (!formData.repoUrl.trim()) {
      alert('请输入 Git 仓库地址')
      return
    }

    if (!validateRepoUrl(formData.repoUrl)) {
      alert('请输入有效的 Git 仓库地址')
      return
    }

    if (!config.selectedRagTag) {
      alert('请先选择知识库标签')
      return
    }

    setIsUploading(true)
    setUploadResult(null)
    setUploadStatus('准备开始...')

    try {
      const uploadData: GitRepoUploadType = {
        ...formData,
        ragTag: config.selectedRagTag,
      }

      const result = await ragApiService.uploadGitRepo(
        uploadData,
        setUploadStatus
      )

      setUploadResult({
        type: 'success',
        message: result,
      })

      onUploadComplete?.(result)

      // 清空表单（除了 ragTag）
      setFormData(prev => ({
        ...prev,
        repoUrl: '',
        username: '',
        password: '',
        branch: 'main',
      }))

    } catch (error) {
      console.error('Git repo upload failed:', error)
      const errorMessage = error instanceof Error ? error.message : '上传失败，请重试'
      setUploadResult({
        type: 'error',
        message: errorMessage,
      })
    } finally {
      setIsUploading(false)
      setUploadStatus('')
    }
  }, [formData, config.selectedRagTag, onUploadComplete])

  return (
    <Card className="p-6 max-w-2xl mx-auto">
      <div className="space-y-6">
        {/* 标题 */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            Git 仓库上传
          </h3>
          {onClose && (
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* 当前选择的知识库 */}
        <div className="text-sm text-muted-foreground">
          当前知识库：<span className="font-medium">{config.selectedRagTag || '未选择'}</span>
        </div>

        {/* 表单 */}
        <div className="space-y-4">
          {/* Git 仓库地址 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Git 仓库地址 <span className="text-red-500">*</span>
            </label>
            <Input
              type="url"
              placeholder="https://github.com/username/repository.git"
              value={formData.repoUrl}
              onChange={(e) => updateFormData('repoUrl', e.target.value)}
              disabled={isUploading}
            />
            <p className="text-xs text-muted-foreground">
              支持 GitHub、GitLab 等 Git 仓库地址
            </p>
          </div>

          {/* 分支名称 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">分支名称</label>
            <Input
              placeholder="main"
              value={formData.branch}
              onChange={(e) => updateFormData('branch', e.target.value)}
              disabled={isUploading}
            />
            <p className="text-xs text-muted-foreground">
              默认为 main 分支，如需其他分支请指定
            </p>
          </div>

          {/* 用户名 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">用户名（可选）</label>
            <Input
              placeholder="Git 用户名"
              value={formData.username}
              onChange={(e) => updateFormData('username', e.target.value)}
              disabled={isUploading}
            />
            <p className="text-xs text-muted-foreground">
              私有仓库需要提供用户名和密码
            </p>
          </div>

          {/* 密码 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">密码/Token（可选）</label>
            <div className="relative">
              <Input
                type={showPassword ? 'text' : 'password'}
                placeholder="Git 密码或 Personal Access Token"
                value={formData.password}
                onChange={(e) => updateFormData('password', e.target.value)}
                disabled={isUploading}
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-full px-3"
                onClick={() => setShowPassword(!showPassword)}
                disabled={isUploading}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              建议使用 Personal Access Token 而不是密码
            </p>
          </div>
        </div>

        {/* 上传状态 */}
        {isUploading && (
          <div className="p-4 bg-muted/50 rounded-lg">
            <div className="flex items-center gap-3">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">{uploadStatus}</span>
            </div>
          </div>
        )}

        {/* 上传结果 */}
        {uploadResult && (
          <div className={`p-4 rounded-lg ${uploadResult.type === 'success'
              ? 'bg-green-50 border border-green-200'
              : 'bg-red-50 border border-red-200'
            }`}>
            <div className="flex items-center gap-3">
              {uploadResult.type === 'success' ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600" />
              )}
              <span className={`text-sm ${uploadResult.type === 'success' ? 'text-green-800' : 'text-red-800'
                }`}>
                {uploadResult.message}
              </span>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex gap-3">
          <Button
            onClick={handleUpload}
            disabled={!formData.repoUrl.trim() || isUploading || !config.selectedRagTag}
            className="flex-1"
          >
            {isUploading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                上传中...
              </>
            ) : (
              <>
                <GitBranch className="h-4 w-4 mr-2" />
                上传仓库
              </>
            )}
          </Button>
          {onClose && (
            <Button variant="outline" onClick={onClose} disabled={isUploading}>
              取消
            </Button>
          )}
        </div>

        {/* 使用说明 */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p><strong>使用说明：</strong></p>
          <ul className="list-disc list-inside space-y-1 ml-2">
            <li>支持公开和私有 Git 仓库</li>
            <li>私有仓库需要提供用户名和密码/Token</li>
            <li>建议使用 Personal Access Token 提高安全性</li>
            <li>上传过程可能需要几分钟，请耐心等待</li>
            <li>仓库中的所有文件都会被处理并添加到知识库</li>
          </ul>
        </div>
      </div>
    </Card>
  )
}
