import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import {
  Upload,
  GitBranch,
  X,
  FileText,
} from 'lucide-react'
import FileUpload from './FileUpload'
import GitRepoUpload from './GitRepoUpload'

interface UploadManagerProps {
  onUploadComplete?: (message: string) => void
  onClose?: () => void
}

type UploadMode = 'select' | 'file' | 'git'

export default function UploadManager({ onUploadComplete, onClose }: UploadManagerProps) {
  const [mode, setMode] = useState<UploadMode>('select')

  const handleUploadComplete = (message: string) => {
    onUploadComplete?.(message)
    // 上传完成后返回选择模式
    setMode('select')
  }



  if (mode === 'file') {
    return (
      <FileUpload
        onUploadComplete={handleUploadComplete}
        onClose={onClose}
      />
    )
  }

  if (mode === 'git') {
    return (
      <GitRepoUpload
        onUploadComplete={handleUploadComplete}
        onClose={onClose}
      />
    )
  }

  // 选择上传方式
  return (
    <Card className="p-6 max-w-2xl mx-auto">
      <div className="space-y-6">
        {/* 标题 */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">选择上传方式</h3>
          {onClose && (
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* 上传方式选择 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* 文件上传 */}
          <Card
            className="p-6 cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-primary/50"
            onClick={() => setMode('file')}
          >
            <div className="text-center space-y-4">
              <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                <Upload className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h4 className="font-semibold mb-2">文件上传</h4>
                <p className="text-sm text-muted-foreground">
                  上传本地文件到知识库
                </p>
              </div>
              <div className="text-xs text-muted-foreground">
                <p>支持多种文件格式</p>
                <p>批量上传，进度跟踪</p>
              </div>
            </div>
          </Card>

          {/* Git 仓库上传 */}
          <Card
            className="p-6 cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-primary/50"
            onClick={() => setMode('git')}
          >
            <div className="text-center space-y-4">
              <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                <GitBranch className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h4 className="font-semibold mb-2">Git 仓库上传</h4>
                <p className="text-sm text-muted-foreground">
                  从 Git 仓库导入代码和文档
                </p>
              </div>
              <div className="text-xs text-muted-foreground">
                <p>支持 GitHub、GitLab 等</p>
                <p>自动处理整个仓库</p>
              </div>
            </div>
          </Card>
        </div>

        {/* 使用说明 */}
        <div className="bg-muted/50 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <FileText className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
            <div className="text-sm text-muted-foreground">
              <p className="font-medium mb-2">使用说明：</p>
              <ul className="space-y-1 list-disc list-inside">
                <li><strong>文件上传：</strong>适合上传少量本地文件，支持拖拽上传</li>
                <li><strong>Git 仓库上传：</strong>适合导入整个项目代码库，自动处理所有文件</li>
                <li>上传的内容将添加到当前选择的知识库标签中</li>
                <li>上传完成后可以在对话中使用相关知识</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 取消按钮 */}
        {onClose && (
          <div className="flex justify-end">
            <Button variant="outline" onClick={onClose}>
              取消
            </Button>
          </div>
        )}
      </div>
    </Card>
  )
}
