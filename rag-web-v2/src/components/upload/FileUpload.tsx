import { useState, useRef, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import {
  Upload,
  X,
  File,
  CheckCircle,
  AlertCircle,
  Loader2,
} from 'lucide-react'
import { ragApiService } from '@/services'
import { useConfig } from '@/contexts/ConfigContext'
import { SUPPORTED_FILE_TYPES, MAX_FILE_SIZE } from '@/constants'
import type { FileUploadProgress } from '@/services/ragApi'

interface FileUploadProps {
  onUploadComplete?: (message: string) => void
  onClose?: () => void
}

export default function FileUpload({ onUploadComplete, onClose }: FileUploadProps) {
  const { config } = useConfig()
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [uploadProgress, setUploadProgress] = useState<FileUploadProgress[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 处理文件选择
  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return

    const fileArray = Array.from(files)
    const { valid, invalid } = ragApiService.validateFiles(fileArray)

    if (invalid.length > 0) {
      const errorMessages = invalid.map(({ file, reason }) => `${file.name}: ${reason}`)
      alert(`以下文件无法上传：\n${errorMessages.join('\n')}`)
    }

    setSelectedFiles(prev => [...prev, ...valid])
  }, [])

  // 处理拖拽
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    handleFileSelect(e.dataTransfer.files)
  }, [handleFileSelect])

  // 移除文件
  const removeFile = useCallback((index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index))
  }, [])

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 上传文件
  const handleUpload = useCallback(async () => {
    if (selectedFiles.length === 0) {
      alert('请选择要上传的文件')
      return
    }

    if (!config.selectedRagTag) {
      alert('请先选择知识库标签')
      return
    }

    setIsUploading(true)

    try {
      const result = await ragApiService.uploadFiles(
        {
          ragTag: config.selectedRagTag,
          files: selectedFiles,
        },
        setUploadProgress
      )

      onUploadComplete?.(result)

      // 清空文件列表
      setSelectedFiles([])
      setUploadProgress([])

    } catch (error) {
      console.error('Upload failed:', error)
      alert('上传失败，请重试')
    } finally {
      setIsUploading(false)
    }
  }, [selectedFiles, config.selectedRagTag, onUploadComplete])

  return (
    <Card className="p-6 max-w-2xl mx-auto">
      <div className="space-y-6">
        {/* 标题 */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">文件上传</h3>
          {onClose && (
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* 当前选择的知识库 */}
        <div className="text-sm text-muted-foreground">
          当前知识库：<span className="font-medium">{config.selectedRagTag || '未选择'}</span>
        </div>

        {/* 拖拽上传区域 */}
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${dragActive
              ? 'border-primary bg-primary/5'
              : 'border-muted-foreground/25 hover:border-muted-foreground/50'
            }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <p className="text-lg font-medium mb-2">拖拽文件到此处或点击选择</p>
          <p className="text-sm text-muted-foreground mb-4">
            支持的文件类型：{SUPPORTED_FILE_TYPES.join(', ')}
          </p>
          <p className="text-sm text-muted-foreground mb-4">
            最大文件大小：{formatFileSize(MAX_FILE_SIZE)}
          </p>
          <Button
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
          >
            选择文件
          </Button>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept={SUPPORTED_FILE_TYPES.join(',')}
            className="hidden"
            onChange={(e) => handleFileSelect(e.target.files)}
          />
        </div>

        {/* 文件列表 */}
        {selectedFiles.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium">已选择的文件 ({selectedFiles.length})</h4>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {selectedFiles.map((file, index) => {
                const progress = uploadProgress.find(p => p.fileName === file.name)
                return (
                  <div key={`${file.name}-${index}`} className="flex items-center gap-3 p-3 border rounded-lg">
                    <File className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{file.name}</p>
                      <p className="text-xs text-muted-foreground">{formatFileSize(file.size)}</p>
                      {progress && (
                        <div className="mt-2">
                          <div className="flex items-center gap-2 mb-1">
                            {progress.status === 'uploading' && (
                              <Loader2 className="h-3 w-3 animate-spin" />
                            )}
                            {progress.status === 'success' && (
                              <CheckCircle className="h-3 w-3 text-green-500" />
                            )}
                            {progress.status === 'error' && (
                              <AlertCircle className="h-3 w-3 text-red-500" />
                            )}
                            <span className="text-xs">
                              {progress.status === 'uploading' && `上传中 ${progress.progress}%`}
                              {progress.status === 'success' && '上传成功'}
                              {progress.status === 'error' && (progress.error || '上传失败')}
                            </span>
                          </div>
                          <Progress value={progress.progress} className="h-1" />
                        </div>
                      )}
                    </div>
                    {!isUploading && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeFile(index)}
                        className="flex-shrink-0"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {/* 上传按钮 */}
        <div className="flex gap-3">
          <Button
            onClick={handleUpload}
            disabled={selectedFiles.length === 0 || isUploading || !config.selectedRagTag}
            className="flex-1"
          >
            {isUploading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                上传中...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                上传文件
              </>
            )}
          </Button>
          {onClose && (
            <Button variant="outline" onClick={onClose} disabled={isUploading}>
              取消
            </Button>
          )}
        </div>
      </div>
    </Card>
  )
}
