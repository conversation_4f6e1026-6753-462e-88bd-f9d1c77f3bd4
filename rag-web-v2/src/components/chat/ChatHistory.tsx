import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  MessageCircle,
  MoreVertical,
  Trash2,
  Edit3,
  Download,
  Calendar,
} from 'lucide-react'
import { useChatContext } from '@/contexts/ChatContext'
import type { Chat } from '@/types'

interface ChatHistoryProps {
  onChatSelect?: (chat: Chat) => void
}

export default function ChatHistory({ onChatSelect }: ChatHistoryProps) {
  const { chats, currentChat, setCurrentChat, deleteChat, updateChatTitle, exportChat } = useChatContext()
  const [editingChatId, setEditingChatId] = useState<string | null>(null)
  const [editingTitle, setEditingTitle] = useState('')
  const [showMenuChatId, setShowMenuChatId] = useState<string | null>(null)

  // 格式化时间
  const formatTime = (timestamp: number): string => {
    const now = Date.now()
    const diff = now - timestamp
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 1) return '刚刚'
    if (minutes < 60) return `${minutes}分钟前`
    if (hours < 24) return `${hours}小时前`
    if (days < 7) return `${days}天前`
    
    return new Date(timestamp).toLocaleDateString()
  }

  // 处理聊天选择
  const handleChatSelect = (chat: Chat) => {
    setCurrentChat(chat)
    onChatSelect?.(chat)
    setShowMenuChatId(null)
  }

  // 开始编辑标题
  const startEditTitle = (chat: Chat) => {
    setEditingChatId(chat.id)
    setEditingTitle(chat.title)
    setShowMenuChatId(null)
  }

  // 保存标题编辑
  const saveTitle = () => {
    if (editingChatId && editingTitle.trim()) {
      updateChatTitle(editingChatId, editingTitle.trim())
    }
    setEditingChatId(null)
    setEditingTitle('')
  }

  // 取消编辑
  const cancelEdit = () => {
    setEditingChatId(null)
    setEditingTitle('')
  }

  // 处理删除聊天
  const handleDeleteChat = (chatId: string) => {
    if (confirm('确定要删除这个对话吗？此操作无法撤销。')) {
      deleteChat(chatId)
    }
    setShowMenuChatId(null)
  }

  // 处理导出聊天
  const handleExportChat = (chatId: string) => {
    try {
      const exportData = exportChat(chatId)
      const chat = chats.find(c => c.id === chatId)
      const filename = `chat_${chat?.title || 'untitled'}_${new Date().toISOString().split('T')[0]}.json`
      
      const blob = new Blob([exportData], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Export failed:', error)
      alert('导出失败，请重试')
    }
    setShowMenuChatId(null)
  }

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      saveTitle()
    } else if (e.key === 'Escape') {
      cancelEdit()
    }
  }

  if (chats.length === 0) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        <MessageCircle className="h-12 w-12 mx-auto mb-3 opacity-50" />
        <p className="text-sm">还没有对话历史</p>
        <p className="text-xs mt-1">开始新对话来创建历史记录</p>
      </div>
    )
  }

  return (
    <div className="space-y-1">
      {chats.map((chat) => (
        <div
          key={chat.id}
          className={`group relative p-3 rounded-lg cursor-pointer transition-colors ${
            currentChat?.id === chat.id
              ? 'bg-primary/10 border border-primary/20'
              : 'hover:bg-accent/50'
          }`}
          onClick={() => handleChatSelect(chat)}
        >
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              {editingChatId === chat.id ? (
                <input
                  type="text"
                  value={editingTitle}
                  onChange={(e) => setEditingTitle(e.target.value)}
                  onBlur={saveTitle}
                  onKeyDown={handleKeyPress}
                  className="w-full text-sm font-medium bg-transparent border-b border-primary focus:outline-none"
                  autoFocus
                  onClick={(e) => e.stopPropagation()}
                />
              ) : (
                <div className="font-medium text-sm truncate mb-1">
                  {chat.title}
                </div>
              )}
              
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <Calendar className="h-3 w-3" />
                <span>{formatTime(chat.updatedAt)}</span>
                {chat.ragTag && (
                  <>
                    <span>•</span>
                    <span className="px-1.5 py-0.5 bg-primary/10 text-primary rounded text-xs">
                      {chat.ragTag}
                    </span>
                  </>
                )}
                <span>•</span>
                <span>{chat.messages.length} 条消息</span>
              </div>
            </div>

            {/* 操作菜单 */}
            <div className="relative">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={(e) => {
                  e.stopPropagation()
                  setShowMenuChatId(showMenuChatId === chat.id ? null : chat.id)
                }}
              >
                <MoreVertical className="h-3 w-3" />
              </Button>

              {showMenuChatId === chat.id && (
                <div className="absolute right-0 top-full mt-1 bg-popover border rounded-md shadow-md z-50 min-w-[120px]">
                  <div className="py-1">
                    <button
                      className="w-full px-3 py-1.5 text-left text-sm hover:bg-accent flex items-center gap-2"
                      onClick={(e) => {
                        e.stopPropagation()
                        startEditTitle(chat)
                      }}
                    >
                      <Edit3 className="h-3 w-3" />
                      重命名
                    </button>
                    <button
                      className="w-full px-3 py-1.5 text-left text-sm hover:bg-accent flex items-center gap-2"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleExportChat(chat.id)
                      }}
                    >
                      <Download className="h-3 w-3" />
                      导出
                    </button>
                    <button
                      className="w-full px-3 py-1.5 text-left text-sm hover:bg-accent text-destructive flex items-center gap-2"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleDeleteChat(chat.id)
                      }}
                    >
                      <Trash2 className="h-3 w-3" />
                      删除
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
