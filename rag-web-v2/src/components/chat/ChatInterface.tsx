import { useCallback, useRef } from 'react'
import { useConfig } from '@/contexts/ConfigContext'
import { useChatContext } from '@/contexts/ChatContext'
import { streamService, API_CONFIG } from '@/services'
import ChatContainer from './ChatContainer'
import MessageInput from './MessageInput'

interface ChatInterfaceProps {
  onFileUpload?: (files: FileList) => void
}

export default function ChatInterface({
  onFileUpload,
}: ChatInterfaceProps) {
  const { config } = useConfig()
  const { currentChat, addMessage, updateMessage } = useChatContext()
  const streamControllerRef = useRef<{ abort: () => void } | null>(null)



  // 更新最后一条消息
  const updateLastMessage = useCallback((content: string) => {
    if (currentChat && currentChat.messages.length > 0) {
      const lastMessage = currentChat.messages[currentChat.messages.length - 1]
      if (lastMessage.role === 'assistant') {
        updateMessage(lastMessage.id, { content })
      }
    }
  }, [currentChat, updateMessage])

  // 处理发送消息
  const handleSendMessage = useCallback(async (content: string) => {
    if (!currentChat) {
      return
    }

    // 添加用户消息
    addMessage({
      role: 'user',
      content,
    })

    // 添加空的助手消息用于流式更新
    addMessage({
      role: 'assistant',
      content: '',
      isStreaming: true,
    })

    // 构建 SSE URL
    const endpoint = config.selectedRagTag
      ? API_CONFIG.ENDPOINTS[config.apiType].CHAT_RAG
      : API_CONFIG.ENDPOINTS[config.apiType].CHAT

    const params = new URLSearchParams({
      model: config.selectedModel,
      message: content,
    })

    if (config.selectedRagTag) {
      params.append('ragTag', config.selectedRagTag)
    }

    const url = `${API_CONFIG.BASE_URL}${endpoint}?${params.toString()}`

    let accumulatedContent = ''

    // 创建流式连接
    const controller = streamService.createStream(url, {
      onMessage: (chunk: string) => {
        accumulatedContent += chunk
        updateLastMessage(accumulatedContent)
      },
      onComplete: () => {
        streamControllerRef.current = null
        // 移除 isStreaming 标记
        if (currentChat && currentChat.messages.length > 0) {
          const lastMessage = currentChat.messages[currentChat.messages.length - 1]
          if (lastMessage.role === 'assistant') {
            updateMessage(lastMessage.id, { isStreaming: false })
          }
        }
      },
      onError: (error) => {
        console.error('Stream error:', error)
        streamControllerRef.current = null
        updateLastMessage('抱歉，发生了错误，请稍后重试。')
      },
    })

    streamControllerRef.current = controller
  }, [config, addMessage, updateLastMessage])

  // 停止生成
  const handleStopGeneration = useCallback(() => {
    if (streamControllerRef.current) {
      streamControllerRef.current.abort()
      streamControllerRef.current = null
    }
  }, [])

  // 处理复制消息
  const handleCopyMessage = useCallback(async (content: string) => {
    try {
      await navigator.clipboard.writeText(content)
      // 这里可以添加成功提示
    } catch (error) {
      console.error('复制失败:', error)
    }
  }, [])

  // 处理重新生成消息
  const handleRegenerateMessage = useCallback((messageId: string) => {
    if (!currentChat) return

    // 找到要重新生成的消息
    const messageIndex = currentChat.messages.findIndex(msg => msg.id === messageId)
    if (messageIndex === -1) return

    // 获取该消息之前的最后一个用户消息
    const userMessages = currentChat.messages.slice(0, messageIndex).filter(msg => msg.role === 'user')
    const lastUserMessage = userMessages[userMessages.length - 1]

    if (lastUserMessage) {
      // 暂时先重新发送消息，后续可以优化为删除后续消息
      handleSendMessage(lastUserMessage.content)
    }
  }, [currentChat, handleSendMessage])



  return (
    <div className="flex flex-col h-full">
      {/* 聊天容器 */}
      <div className="flex-1 min-h-0">
        <ChatContainer
          messages={currentChat?.messages || []}
          isLoading={!!streamControllerRef.current}
          onCopyMessage={handleCopyMessage}
          onRegenerateMessage={handleRegenerateMessage}
        />
      </div>

      {/* 输入区域 */}
      <div className="flex-shrink-0 p-4 border-t bg-card/50 backdrop-blur-sm">
        <MessageInput
          onSendMessage={handleSendMessage}
          onFileUpload={onFileUpload}
          isLoading={!!streamControllerRef.current}
          onStopGeneration={handleStopGeneration}
          disabled={!!streamControllerRef.current || !currentChat}
        />
      </div>
    </div>
  )
}
