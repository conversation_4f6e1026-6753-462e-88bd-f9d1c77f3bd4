// HTTP 服务
export { httpService, HttpService } from './http'

// 聊天 API 服务
export { chatApiService, ChatApiService } from './chatApi'
export type { ChatRequest, StreamChatRequest } from './chatApi'

// 模型 API 服务
export { modelApiService, ModelApiService } from './modelApi'

// RAG API 服务
export { ragApiService, RagApiService } from './ragApi'
export type { FileUploadRequest, FileUploadProgress } from './ragApi'

// 流式服务
export { streamService, StreamService, cleanupStreams } from './streamService'
export type { StreamOptions, StreamController } from './streamService'

// 错误处理
export { errorHandler, globalErrorHandler, ErrorHandler, GlobalErrorHandler } from './errorHandler'
export type { RetryOptions, ErrorInfo } from './errorHandler'

// 存储服务
export { storageService, StorageService } from './storage'
export type { StorageOptions } from './storage'

// 常量
export { API_CONFIG } from '@/constants'
