import type { Chat, AppConfig, Message } from '../types'
import { STORAGE_KEYS, DEFAULT_CONFIG } from '@/constants'

export interface StorageOptions {
  prefix?: string
  version?: string
}

export class StorageService {
  private prefix: string
  private version: string

  constructor(options: StorageOptions = {}) {
    this.prefix = options.prefix || 'rag-web'
    this.version = options.version || '1.0'
  }

  // 生成存储键名
  private getKey(key: string): string {
    return `${this.prefix}_${this.version}_${key}`
  }

  // 通用存储方法
  private setItem<T>(key: string, value: T): void {
    try {
      const serializedValue = JSON.stringify({
        data: value,
        timestamp: Date.now(),
        version: this.version,
      })
      localStorage.setItem(this.getKey(key), serializedValue)
    } catch (error) {
      console.error('[Storage] Set item error:', error)
      throw new Error('存储数据失败')
    }
  }

  // 通用获取方法
  private getItem<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = localStorage.getItem(this.getKey(key))
      if (!item) {
        return defaultValue || null
      }

      const parsed = JSON.parse(item)

      // 检查版本兼容性
      if (parsed.version !== this.version) {
        console.warn(`[Storage] Version mismatch for ${key}, clearing data`)
        this.removeItem(key)
        return defaultValue || null
      }

      return parsed.data
    } catch (error) {
      console.error('[Storage] Get item error:', error)
      return defaultValue || null
    }
  }

  // 删除存储项
  private removeItem(key: string): void {
    try {
      localStorage.removeItem(this.getKey(key))
    } catch (error) {
      console.error('[Storage] Remove item error:', error)
    }
  }

  // 配置相关方法
  saveConfig(config: AppConfig): void {
    this.setItem(STORAGE_KEYS.CONFIG, config)
  }

  getConfig(): AppConfig {
    return this.getItem(STORAGE_KEYS.CONFIG, DEFAULT_CONFIG) || DEFAULT_CONFIG
  }

  updateConfig(updates: Partial<AppConfig>): AppConfig {
    const currentConfig = this.getConfig()
    const newConfig = { ...currentConfig, ...updates }
    this.saveConfig(newConfig)
    return newConfig
  }

  // 对话相关方法
  saveChats(chats: Chat[]): void {
    this.setItem(STORAGE_KEYS.CHATS, chats)
  }

  getChats(): Chat[] {
    return this.getItem(STORAGE_KEYS.CHATS, []) || []
  }

  saveChat(chat: Chat): void {
    const chats = this.getChats()
    const existingIndex = chats.findIndex(c => c.id === chat.id)

    if (existingIndex >= 0) {
      chats[existingIndex] = chat
    } else {
      chats.push(chat)
    }

    this.saveChats(chats)
  }

  deleteChat(chatId: string): void {
    const chats = this.getChats()
    const filteredChats = chats.filter(c => c.id !== chatId)
    this.saveChats(filteredChats)
  }

  getChat(chatId: string): Chat | null {
    const chats = this.getChats()
    return chats.find(c => c.id === chatId) || null
  }

  // 消息相关方法
  addMessageToChat(chatId: string, message: Message): void {
    const chat = this.getChat(chatId)
    if (chat) {
      chat.messages.push(message)
      chat.updatedAt = Date.now()
      this.saveChat(chat)
    }
  }

  updateMessageInChat(chatId: string, messageId: string, updates: Partial<Message>): void {
    const chat = this.getChat(chatId)
    if (chat) {
      const messageIndex = chat.messages.findIndex((m: Message) => m.id === messageId)
      if (messageIndex >= 0) {
        chat.messages[messageIndex] = { ...chat.messages[messageIndex], ...updates }
        chat.updatedAt = Date.now()
        this.saveChat(chat)
      }
    }
  }

  // 导出数据
  exportData(): {
    config: AppConfig
    chats: Chat[]
    exportTime: number
    version: string
  } {
    return {
      config: this.getConfig(),
      chats: this.getChats(),
      exportTime: Date.now(),
      version: this.version,
    }
  }

  // 导入数据
  importData(data: {
    config?: AppConfig
    chats?: Chat[]
    version?: string
  }): void {
    try {
      if (data.config) {
        this.saveConfig(data.config)
      }

      if (data.chats) {
        this.saveChats(data.chats)
      }

      console.log('[Storage] Data imported successfully')
    } catch (error) {
      console.error('[Storage] Import data error:', error)
      throw new Error('导入数据失败')
    }
  }

  // 清空所有数据
  clearAll(): void {
    try {
      this.removeItem(STORAGE_KEYS.CONFIG)
      this.removeItem(STORAGE_KEYS.CHATS)
      console.log('[Storage] All data cleared')
    } catch (error) {
      console.error('[Storage] Clear all error:', error)
      throw new Error('清空数据失败')
    }
  }

  // 获取存储使用情况
  getStorageInfo(): {
    used: number
    available: number
    total: number
    percentage: number
  } {
    try {
      let used = 0

      // 计算已使用的存储空间
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith(this.prefix)) {
          const value = localStorage.getItem(key)
          if (value) {
            used += key.length + value.length
          }
        }
      }

      // 估算总可用空间（通常为 5-10MB）
      const total = 5 * 1024 * 1024 // 5MB
      const available = total - used
      const percentage = (used / total) * 100

      return {
        used,
        available,
        total,
        percentage,
      }
    } catch (error) {
      console.error('[Storage] Get storage info error:', error)
      return {
        used: 0,
        available: 0,
        total: 0,
        percentage: 0,
      }
    }
  }
}

// 默认存储服务实例
export const storageService = new StorageService()
