import { httpService } from './http'
import { API_CONFIG } from '@/constants'
import type { ApiType, ChatResponse } from '../types'

export interface ChatRequest {
  model: string
  message: string
  ragTag?: string
}

export interface StreamChatRequest extends ChatRequest {
  onMessage?: (content: string) => void
  onComplete?: () => void
  onError?: (error: Error) => void
}

export class ChatApiService {
  // 发送普通聊天消息
  async sendMessage(
    apiType: ApiType,
    request: ChatRequest
  ): Promise<ChatResponse> {
    const endpoint = this.getEndpoint(apiType, 'GENERATE')

    try {
      const response = await httpService.get<ChatResponse>(endpoint, {
        params: {
          model: request.model,
          message: request.message,
        },
      })

      return response
    } catch (error) {
      console.error('[ChatAPI] Send message error:', error)
      throw error
    }
  }

  // 发送带知识库的聊天消息
  async sendMessageWithRAG(
    apiType: ApiType,
    request: ChatRequest & { ragTag: string }
  ): Promise<ChatResponse> {
    const endpoint = this.getEndpoint(apiType, 'CHAT_RAG')

    try {
      const response = await httpService.get<ChatResponse>(endpoint, {
        params: {
          model: request.model,
          message: request.message,
          ragTag: request.ragTag,
        },
      })

      return response
    } catch (error) {
      console.error('[ChatAPI] Send RAG message error:', error)
      throw error
    }
  }

  // 发送流式聊天消息
  async sendStreamMessage(
    apiType: ApiType,
    request: StreamChatRequest
  ): Promise<void> {
    const endpoint = this.getEndpoint(apiType, 'CHAT')
    const url = `${API_CONFIG.BASE_URL}${endpoint}`

    try {
      // 构建查询参数
      const params = new URLSearchParams({
        model: request.model,
        message: request.message,
      })

      if (request.ragTag) {
        params.append('ragTag', request.ragTag)
      }

      const fullUrl = `${url}?${params.toString()}`

      // 创建 EventSource 连接
      const eventSource = new EventSource(fullUrl)

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)

          // 处理实际的数据格式：data.result.output.text
          if (data.result?.output?.text) {
            const content = data.result.output.text
            request.onMessage?.(content)
          }

          // 检查是否完成 - 根据 finishReason 判断
          if (data.result?.output?.metadata?.finishReason === 'STOP' ||
            data.result?.metadata?.finishReason === 'STOP') {
            eventSource.close()
            request.onComplete?.()
          }

          // 兼容原始格式
          if (data.choices && data.choices[0]?.message?.content) {
            const content = data.choices[0].message.content
            request.onMessage?.(content)
          }

          // 检查是否完成
          if (data.choices && data.choices[0]?.finish_reason === 'stop') {
            eventSource.close()
            request.onComplete?.()
          }
        } catch (error) {
          console.error('[ChatAPI] Parse stream data error:', error)
          request.onError?.(new Error('解析响应数据失败'))
        }
      }

      eventSource.onerror = (error) => {
        console.error('[ChatAPI] Stream error:', error)
        eventSource.close()
        request.onError?.(new Error('流式连接错误'))
      }

      // 返回一个 Promise，在连接关闭时 resolve
      return new Promise((resolve, reject) => {
        eventSource.addEventListener('close', () => resolve())
        eventSource.addEventListener('error', (error) => reject(error))
      })

    } catch (error) {
      console.error('[ChatAPI] Send stream message error:', error)
      throw error
    }
  }

  // 发送带知识库的流式聊天消息
  async sendStreamMessageWithRAG(
    apiType: ApiType,
    request: StreamChatRequest & { ragTag: string }
  ): Promise<void> {
    const endpoint = this.getEndpoint(apiType, 'CHAT_RAG')
    const url = `${API_CONFIG.BASE_URL}${endpoint}`

    try {
      const params = new URLSearchParams({
        model: request.model,
        message: request.message,
        ragTag: request.ragTag,
      })

      const fullUrl = `${url}?${params.toString()}`
      const eventSource = new EventSource(fullUrl)

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)

          // 处理实际的数据格式：data.result.output.text
          if (data.result?.output?.text) {
            const content = data.result.output.text
            request.onMessage?.(content)
          }

          // 检查是否完成 - 根据 finishReason 判断
          if (data.result?.output?.metadata?.finishReason === 'STOP' ||
            data.result?.metadata?.finishReason === 'STOP') {
            eventSource.close()
            request.onComplete?.()
          }

          // 兼容原始格式
          if (data.choices && data.choices[0]?.message?.content) {
            const content = data.choices[0].message.content
            request.onMessage?.(content)
          }

          if (data.choices && data.choices[0]?.finish_reason === 'stop') {
            eventSource.close()
            request.onComplete?.()
          }
        } catch (error) {
          console.error('[ChatAPI] Parse RAG stream data error:', error)
          request.onError?.(new Error('解析响应数据失败'))
        }
      }

      eventSource.onerror = (error) => {
        console.error('[ChatAPI] RAG stream error:', error)
        eventSource.close()
        request.onError?.(new Error('流式连接错误'))
      }

      return new Promise((resolve, reject) => {
        eventSource.addEventListener('close', () => resolve())
        eventSource.addEventListener('error', (error) => reject(error))
      })

    } catch (error) {
      console.error('[ChatAPI] Send RAG stream message error:', error)
      throw error
    }
  }

  // 获取 API 端点
  private getEndpoint(apiType: ApiType, type: 'CHAT' | 'CHAT_RAG' | 'GENERATE'): string {
    const endpoints = API_CONFIG.ENDPOINTS[apiType]
    return endpoints[type]
  }
}

// 默认服务实例
export const chatApiService = new ChatApiService()
