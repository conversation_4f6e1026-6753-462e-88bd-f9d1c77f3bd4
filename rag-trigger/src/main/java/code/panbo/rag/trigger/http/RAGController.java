package code.panbo.rag.trigger.http;

import code.panbo.rag.api.IRAGService;
import code.panbo.rag.api.response.Response;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.springframework.ai.document.Document;
import org.springframework.ai.reader.tika.TikaDocumentReader;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
import org.springframework.core.io.PathResource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/rag/")
@Slf4j
public class RAGController implements IRAGService {

    /**
     * Redisson客户端中存储的标签列表的名称
     */
    public static final String RAG_TAG_LIST = "rag_tag";
    /**
     * 元数据中的知识库名称
     */
    public static final String KNOWLEDGE_METADATA = "knowledge";
    /**
     * 知识库的名称
     */
    public static final String KNOWLEDGE_BASE = "knowledge_base";

    @Resource
    private TokenTextSplitter tokenTextSplitter;
    @Resource
    private PgVectorStore pgVectorStore;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 下载git仓库到本地
     *
     * @param repoUrl   git仓库地址
     * @param username  用户名
     * @param password  密码
     * @param localPath 本地保存地址
     * @throws IOException     IOException
     * @throws GitAPIException GitAPIException
     */
    private static void downloadRepoToLocalPath(String repoUrl, String username, String password, String localPath) throws IOException, GitAPIException {
        // 先清空本地仓库
        FileUtils.deleteDirectory(new File(localPath));

        // clone repository
        Git gitRepo = Git.cloneRepository()
                .setURI(repoUrl)
                .setDirectory(new File(localPath))
                .setCredentialsProvider(new UsernamePasswordCredentialsProvider(username, password))
                .call();

        // 关闭 Git 仓库
        gitRepo.close();

        log.info("Git 仓库下载完成，保存路径为：{}", localPath);
    }

    /**
     * createQueryTagList 查询标签列表
     *
     * @return 标签列表
     */
    @GetMapping("/tags")
    @Override
    public Response<List<String>> queryRagTagList() {
        // 获取Redisson客户端中的列表元素
        RList<String> elements = redissonClient.getList(RAG_TAG_LIST);
        // 构建并返回包含标签列表的响应对象
        return Response.<List<String>>builder()
                .code("0000")
                .info("调用成功")
                .data(elements)
                .build();
    }

    /**
     * 上传文件到知识库
     *
     * @param ragTag 标签
     * @param files  文件列表
     * @return 上传成功信息
     */
    @PostMapping("/file/upload")
    @Override
    public Response<String> uploadFile(@RequestParam("ragTag") String ragTag, @RequestParam("files") List<MultipartFile> files) {

        log.info("开始向知识库上传文件！");

        for (MultipartFile file : files) {
            uploadFileToRag(ragTag, file);
        }

        addRagTagToRedis(ragTag);

        // 构建并返回包含上传成功信息的响应对象
        return Response.<String>builder()
                .code("0000")
                .info("上传成功")
                .data("上传成功")
                .build();
    }

    /**
     * 将文件上传到RAG（Retrieval-Augmented Generation）系统
     * 此方法负责读取文件内容，拆分文档，并将拆分后的文档以及原始文档与RAG标签关联，最后存储到向量数据库中
     *
     * @param ragTag RAG标签，用于标识文件属于哪个知识库
     * @param file   要上传的文件，作为MultipartFile对象传递
     */
    private void uploadFileToRag(String ragTag, MultipartFile file) {
        // 源文件保存到 minio

        // 使用TikaDocumentReader读取文件内容，将其转换为Document对象
        TikaDocumentReader reader = new TikaDocumentReader(file.getResource());
        List<Document> documents = reader.get();

        // 使用tokenTextSplitter拆分文档，以便于处理和存储
        List<Document> documentSplitterList = tokenTextSplitter.apply(documents);

        // 为原始文档和拆分后的文档添加知识库标签元数据
        documents.forEach(doc -> doc.getMetadata().put(KNOWLEDGE_METADATA, ragTag));
        documentSplitterList.forEach(doc -> doc.getMetadata().put(KNOWLEDGE_METADATA, ragTag));

        // 存储到向量数据库
        pgVectorStore.accept(documentSplitterList);

        // 记录上传完成的日志信息
        log.info("上传完成，文件名为：{}", file.getOriginalFilename());
    }

    /**
     * 上传Git仓库到知识库
     *
     * @param ragTag   仓库标签，用于标识仓库
     * @param repoUrl  Git仓库URL
     * @param username 访问仓库的用户名
     * @param password 访问仓库的密码
     * @return 返回上传结果的响应
     * @throws IOException     当文件操作出错时抛出
     * @throws GitAPIException 当Git操作出错时抛出
     */
    @PostMapping("/git-repo/upload")
    @Override
    public Response<String> uploadGitRepo(String ragTag, @RequestParam("repoUrl") String repoUrl, @RequestParam("username") String username, @RequestParam("password") String password) throws IOException, GitAPIException {

        // 记录开始上传的日志
        log.info("开始向知识库上传git仓库！");

        // 默认的本地保存地址
        String localPath = "./git_repo/";

        // 如果提供了仓库标签，则使用标签作为本地路径的一部分
        if (StringUtils.isNotBlank(ragTag)) {
            localPath += ragTag;
        } else {
            // 如果未提供仓库标签，获取仓库名称作为tag，截取最后的名称，“.git”之前的部分
            String[] split = repoUrl.split("/");
            String repoName = split[split.length - 1];
            localPath += repoName.substring(0, repoName.lastIndexOf("."));
        }

        // 下载Git仓库到本地指定路径
        downloadRepoToLocalPath(repoUrl, username, password, localPath);

        // 将下载的Git仓库上传到知识库
        uploadGitRepoToRag(ragTag, localPath);

        // 删除本地下载的Git仓库
        FileUtils.deleteDirectory(new File(localPath));

        // 添加Redisson标签，用于标识上传的仓库
        addRagTagToRedis(ragTag);

        // 构建并返回上传成功的响应
        return Response.<String>builder()
                .code("0000")
                .info("上传成功")
                .data("上传成功")
                .build();
    }

    /**
     * 上传git仓库到知识库
     *
     * @param ragTag    标签
     * @param localPath 本地保存地址
     */
    private void uploadGitRepoToRag(String ragTag, String localPath) throws IOException {
        // 获取当前目录
        Files.walkFileTree(Paths.get(localPath), new SimpleFileVisitor<>() {

            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
                // 打印文件名
                log.info("文件路径是： {}", file.toAbsolutePath());

                try {
                    PathResource pathResource = new PathResource(file);
                    TikaDocumentReader reader = new TikaDocumentReader(pathResource);
                    List<Document> documents = reader.get();
                    List<Document> documentSplitterList = tokenTextSplitter.apply(documents);

                    documents.forEach(doc -> doc.getMetadata().put("knowledge", ragTag));
                    documentSplitterList.forEach(doc -> doc.getMetadata().put("knowledge", ragTag));

                    // 存储到向量数据库
                    pgVectorStore.accept(documentSplitterList);
                } catch (Exception e) {
                    log.error("文件信息上传失败，文件名为：{}", file.getFileName());
                }

                return FileVisitResult.CONTINUE;
            }
        });

        log.info("git 仓库上传完成！！");
    }

    /**
     * 添加Redisson标签
     * <p>
     * 此方法用于将特定的标签添加到Redisson客户端的标签列表中如果标签已经存在，则不会重复添加
     *
     * @param ragTag 需要添加到列表中的标签字符串
     */
    private void addRagTagToRedis(String ragTag) {

        // 向Redisson客户端中添加标签
        RList<String> elements = redissonClient.getList(RAG_TAG_LIST);
        // 如果标签列表中不存在该标签，则添加
        if (!elements.contains(ragTag)) {
            elements.add(ragTag);
        }

        log.info("添加标签成功，标签为：{}", ragTag);
    }
}
