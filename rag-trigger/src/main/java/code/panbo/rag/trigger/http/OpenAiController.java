package code.panbo.rag.trigger.http;

import code.panbo.rag.api.IAiService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.ai.document.Document;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * OpenAI 控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/openai")
@Slf4j
public class OpenAiController implements IAiService {

    @Resource
    private OpenAiChatModel chatModel;

    @Resource
    private PgVectorStore pgVectorStore;

    @Override
    @GetMapping("/generate")
    public ChatResponse generate(@RequestParam("model") String model, @RequestParam("message") String message) {
        return chatModel.call(
                new Prompt(
                        message,
                        OpenAiChatOptions.builder()
                                .model(model)
                                .build()
                ));
    }

    @Override
    @GetMapping(value = "/generate_stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE + ";charset=UTF-8")
    public Flux<ChatResponse> generateStream(@RequestParam("model") String model, @RequestParam("message") String message) {
        log.info("OpenAI 流式对话请求 - 模型: {}, 消息: {}", model, message);
        try {
            Flux<ChatResponse> responseFlux = chatModel.stream(
                            new Prompt(
                                    message,
                                    OpenAiChatOptions.builder()
                                            .model(model)
                                            .build()
                            ))
                    .onErrorResume(e -> {
                        log.error("OpenAI 流式对话请求异常", e);
                        return Flux.empty();
                    })
                    .timeout(Duration.ofSeconds(60), Flux.empty()); // 设置超时

            return responseFlux
                    .doOnCancel(() -> log.info("客户端取消了请求连接"))
                    .doOnError(e -> log.error("流处理过程中发生错误", e))
                    .doOnComplete(() -> log.info("流式对话完成"));

        } catch (Exception e) {
            log.error("OpenAI 流式对话请求异常", e);
            return Flux.empty();
        }
    }

    @Override
    @GetMapping(value = "/generate_stream_rag", produces = MediaType.TEXT_EVENT_STREAM_VALUE + ";charset=UTF-8")
    public Flux<ChatResponse> generateStreamRag(@RequestParam("model") String model, @RequestParam("ragTag") String ragTag, @RequestParam("message") String message) {
        log.info("OpenAI RAG流式对话请求 - 模型: {}, RAG标签: {}, 消息: {}", model, ragTag, message);

        try {
            // 设置默认的系统提示词
            String SYSTEM_PROMPT = """
                    Use the information from the DOCUMENTS section to provide accurate answers but act as if you knew this information innately.
                    If unsure, simply state that you don't know.
                    Another thing you need to note is that your reply must be in Chinese!
                    DOCUMENTS:
                        {documents}
                    """;

            // 指定文档搜索
            SearchRequest request = SearchRequest.builder()
                    .query(message)
                    .topK(5)
                    .filterExpression("knowledge == '" + ragTag + "'")
                    .build();

            // 使用pgVectorStore进行相似度搜索，获取相关文档列表
            List<Document> documents = pgVectorStore.similaritySearch(request);
            if (documents != null) {
                log.info("找到 {} 个相关文档", documents.size());
            }

            // 将文档列表中的内容收集并拼接成一个字符串
            String documentCollectors = null;
            if (documents != null) {
                documentCollectors = documents.stream().map(Document::getText).collect(Collectors.joining());
            }

            // 创建一个系统提示消息，包含收集到的文档内容
            Message ragMessage = null;
            if (documentCollectors != null) {
                ragMessage = new SystemPromptTemplate(SYSTEM_PROMPT)
                        .createMessage(Map.of("documents", documentCollectors));
            }

            // 初始化消息列表，添加用户消息和系统提示消息
            List<Message> messages = new ArrayList<>();
            messages.add(new UserMessage(message));
            messages.add(ragMessage);

            // 调用chatClient进行对话请求，传入消息列表和配置
            Flux<ChatResponse> responseFlux = chatModel.stream(new Prompt(messages,
                            OpenAiChatOptions.builder()
                                    .model(model)
                                    .build()))
                    .onErrorResume(e -> {
                        log.error("OpenAI RAG流式对话请求异常", e);
                        return Flux.empty();
                    })
                    .timeout(Duration.ofSeconds(120), Flux.empty()); // RAG查询可能更长，给2分钟超时

            return responseFlux
                    .doOnCancel(() -> log.info("客户端取消了RAG请求连接"))
                    .doOnError(e -> log.error("RAG流处理过程中发生错误", e))
                    .doOnComplete(() -> log.info("RAG流式对话完成"));


        } catch (Exception e) {
            log.error("OpenAI RAG流式对话请求异常", e);
            return Flux.empty();
        }
    }
}
